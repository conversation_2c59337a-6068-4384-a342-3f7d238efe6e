package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.model.UnlockType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.connection.Message;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * Test suite for {@link UnlockMessageListener}.
 * <p>
 * Tests ensure that:
 * - Message parsing and processing works correctly
 * - Different unlock types trigger appropriate signaling behavior
 * - Error handling for invalid messages
 * - Asynchronous processing using provided executor
 * - Proper semaphore holder management
 * </p>
 */
@ExtendWith(MockitoExtension.class)
class UnlockMessageListenerTest {

    private static final String BUCKET_NAME = "test-bucket";
    private static final String CHANNEL_PATTERN = "test:*:unlock";
    private static final String LOCK_NAME = "test-lock";
    private static final String CHANNEL = "test:bucket:__unlock_channels__:{" + LOCK_NAME + "}";

    @Mock
    private Executor unlockMessageExecutor;

    @Mock
    private Message message;

    @Mock
    private LockSemaphoreHolder semaphoreHolder;

    private UnlockMessageListener listener;

    @BeforeEach
    void setUp() {
        listener = new UnlockMessageListener(BUCKET_NAME, CHANNEL_PATTERN, unlockMessageExecutor);
    }

    @Test
    @DisplayName("Should create listener with correct properties")
    void shouldCreateListenerWithCorrectProperties() {
        assertThat(listener.getBucketName()).isEqualTo(BUCKET_NAME);
        assertThat(listener.getChannelPattern()).isEqualTo(CHANNEL_PATTERN);
    }

    @Test
    @DisplayName("Should register and unregister semaphore holders")
    void shouldRegisterAndUnregisterSemaphoreHolders() {
        // Register semaphore holder
        LockSemaphoreHolder registered = listener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        
        assertThat(registered).isSameAs(semaphoreHolder);
        
        // Unregister semaphore holder
        LockSemaphoreHolder unregistered = listener.unregisterLockSemaphoreHolder(LOCK_NAME);
        
        assertThat(unregistered).isSameAs(semaphoreHolder);
        
        // Second unregister should return null
        LockSemaphoreHolder secondUnregister = listener.unregisterLockSemaphoreHolder(LOCK_NAME);
        assertThat(secondUnregister).isNull();
    }

    @Test
    @DisplayName("Should process unlock message asynchronously")
    void shouldProcessUnlockMessageAsynchronously() throws InterruptedException {
        // Setup
        when(message.getChannel()).thenReturn(CHANNEL.getBytes(StandardCharsets.UTF_8));
        when(message.getBody()).thenReturn("REENTRANT_FULLY_RELEASED".getBytes(StandardCharsets.UTF_8));
        
        // Use a real executor to verify async behavior
        CountDownLatch latch = new CountDownLatch(1);
        Executor realExecutor = task -> {
            new Thread(() -> {
                task.run();
                latch.countDown();
            }).start();
        };
        
        UnlockMessageListener realListener = new UnlockMessageListener(BUCKET_NAME, CHANNEL_PATTERN, realExecutor);
        realListener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        
        when(semaphoreHolder.getWaitersCount()).thenReturn(1);
        
        // Execute
        realListener.onMessage(message, null);
        
        // Verify async execution
        assertThat(latch.await(1, TimeUnit.SECONDS)).isTrue();
        verify(semaphoreHolder).signal();
    }

    @Test
    @DisplayName("Should signal single waiter for single-waiter unlock types")
    void shouldSignalSingleWaiterForSingleWaiterUnlockTypes() {
        // Test all single-waiter unlock types
        UnlockType[] singleWaiterTypes = {
            UnlockType.REENTRANT_FULLY_RELEASED,
            UnlockType.NON_REENTRANT_RELEASED,
            UnlockType.STATE_LOCK_RELEASED_STATE_UNCHANGED,
            UnlockType.STATE_LOCK_RELEASED_STATE_UPDATED,
            UnlockType.RW_READ_RELEASED_WAKEN_SINGLE_WRITER,
            UnlockType.STAMPED_WRITE_RELEASED,
            UnlockType.STAMPED_CONVERTED_TO_READ,
            UnlockType.STAMPED_CONVERTED_TO_WRITE
        };
        
        for (UnlockType unlockType : singleWaiterTypes) {
            testUnlockTypeSignaling(unlockType, 1, 1);
        }
    }

    @Test
    @DisplayName("Should signal all waiters for multi-waiter unlock types")
    void shouldSignalAllWaitersForMultiWaiterUnlockTypes() {
        // Test all multi-waiter unlock types
        UnlockType[] multiWaiterTypes = {
            UnlockType.RW_READ_RELEASED_WAKEN_READERS,
            UnlockType.RW_WRITE_RELEASED_WAKEN_ALL,
            UnlockType.STAMPED_READ_RELEASED
        };
        
        for (UnlockType unlockType : multiWaiterTypes) {
            testUnlockTypeSignaling(unlockType, 5, 5); // 5 waiters, signal all 5
        }
    }

    private void testUnlockTypeSignaling(UnlockType unlockType, int waitersCount, int expectedSignals) {
        // Setup
        when(message.getChannel()).thenReturn(CHANNEL.getBytes(StandardCharsets.UTF_8));
        when(message.getBody()).thenReturn(unlockType.name().getBytes(StandardCharsets.UTF_8));
        
        // Use synchronous executor for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(unlockMessageExecutor).execute(any(Runnable.class));
        
        listener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        when(semaphoreHolder.getWaitersCount()).thenReturn(waitersCount);
        
        // Execute
        listener.onMessage(message, null);
        
        // Verify
        if (expectedSignals == 1) {
            verify(semaphoreHolder).signal();
        } else {
            verify(semaphoreHolder).signal(expectedSignals);
        }
        
        // Reset for next test
        reset(semaphoreHolder, unlockMessageExecutor);
    }

    @Test
    @DisplayName("Should handle invalid unlock type gracefully")
    void shouldHandleInvalidUnlockTypeGracefully() {
        // Setup
        when(message.getChannel()).thenReturn(CHANNEL.getBytes(StandardCharsets.UTF_8));
        when(message.getBody()).thenReturn("INVALID_UNLOCK_TYPE".getBytes(StandardCharsets.UTF_8));
        
        // Use synchronous executor for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(unlockMessageExecutor).execute(any(Runnable.class));

        listener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        lenient().when(semaphoreHolder.getWaitersCount()).thenReturn(1);
        
        // Execute - should not throw exception
        assertThatCode(() -> listener.onMessage(message, null)).doesNotThrowAnyException();
        
        // Verify no signaling occurred
        verify(semaphoreHolder, never()).signal();
        verify(semaphoreHolder, never()).signal(anyInt());
    }

    @Test
    @DisplayName("Should handle missing semaphore holder gracefully")
    void shouldHandleMissingSemaphoreHolderGracefully() {
        // Setup
        when(message.getChannel()).thenReturn(CHANNEL.getBytes(StandardCharsets.UTF_8));
        when(message.getBody()).thenReturn("REENTRANT_FULLY_RELEASED".getBytes(StandardCharsets.UTF_8));
        
        // Use synchronous executor for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(unlockMessageExecutor).execute(any(Runnable.class));
        
        // Don't register any semaphore holder
        
        // Execute - should not throw exception
        assertThatCode(() -> listener.onMessage(message, null)).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("Should handle zero waiters gracefully")
    void shouldHandleZeroWaitersGracefully() {
        // Setup
        when(message.getChannel()).thenReturn(CHANNEL.getBytes(StandardCharsets.UTF_8));
        when(message.getBody()).thenReturn("REENTRANT_FULLY_RELEASED".getBytes(StandardCharsets.UTF_8));
        
        // Use synchronous executor for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(unlockMessageExecutor).execute(any(Runnable.class));
        
        listener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        lenient().when(semaphoreHolder.getWaitersCount()).thenReturn(0);
        
        // Execute - should not throw exception
        assertThatCode(() -> listener.onMessage(message, null)).doesNotThrowAnyException();
        
        // Verify no signaling occurred
        verify(semaphoreHolder, never()).signal();
        verify(semaphoreHolder, never()).signal(anyInt());
    }

    @Test
    @DisplayName("Should extract lock name from channel correctly")
    void shouldExtractLockNameFromChannelCorrectly() {
        // Test various channel formats
        String[] testChannels = {
            "test:bucket:__unlock_channels__:{my-lock}",
            "prefix:bucket:__unlock_channels__:{another-lock}",
            "complex:bucket:__unlock_channels__:{lock:name:with:colons}"
        };

        String[] expectedLockNames = {
            "my-lock",
            "another-lock",
            "lock:name:with:colons"
        };
        
        for (int i = 0; i < testChannels.length; i++) {
            when(message.getChannel()).thenReturn(testChannels[i].getBytes(StandardCharsets.UTF_8));
            when(message.getBody()).thenReturn("REENTRANT_FULLY_RELEASED".getBytes(StandardCharsets.UTF_8));
            
            // Use synchronous executor for testing
            doAnswer(invocation -> {
                Runnable task = invocation.getArgument(0);
                task.run();
                return null;
            }).when(unlockMessageExecutor).execute(any(Runnable.class));
            
            listener.registerLockSemaphoreHolder(expectedLockNames[i], semaphoreHolder);
            when(semaphoreHolder.getWaitersCount()).thenReturn(1);
            
            // Execute
            listener.onMessage(message, null);
            
            // Verify correct semaphore holder was signaled
            verify(semaphoreHolder).signal();
            
            // Reset for next test
            reset(semaphoreHolder, unlockMessageExecutor);
            listener.unregisterLockSemaphoreHolder(expectedLockNames[i]);
        }
    }
}
