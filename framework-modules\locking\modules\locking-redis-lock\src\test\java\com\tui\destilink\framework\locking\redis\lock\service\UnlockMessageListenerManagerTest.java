package com.tui.destilink.framework.locking.redis.lock.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;

import java.util.concurrent.Executor;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Test suite for {@link UnlockMessageListenerManager}.
 * <p>
 * Tests ensure that:
 * - Listeners are created and registered correctly per bucket
 * - Channel patterns are built correctly
 * - Semaphore holders are managed properly
 * - Cleanup works correctly during shutdown
 * - Thread safety for concurrent access
 * </p>
 */
@ExtendWith(MockitoExtension.class)
class UnlockMessageListenerManagerTest {

    private static final String BUCKET_NAME = "test-bucket";
    private static final String KEY_PREFIX = "test:prefix";
    private static final String LOCK_NAME = "test-lock";

    @Mock
    private RedisMessageListenerContainer listenerContainer;

    @Mock
    private Executor unlockMessageExecutor;

    private UnlockMessageListenerManager manager;

    @BeforeEach
    void setUp() {
        manager = new UnlockMessageListenerManager(listenerContainer, unlockMessageExecutor);
    }

    @Test
    @DisplayName("Should create and register listener for new bucket")
    void shouldCreateAndRegisterListenerForNewBucket() {
        // Execute
        UnlockMessageListener listener = manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);

        // Verify
        assertThat(listener).isNotNull();
        assertThat(listener.getBucketName()).isEqualTo(BUCKET_NAME);
        assertThat(listener.getChannelPattern()).isEqualTo("test:prefix:test-bucket:__unlock_channels__:*");

        // Verify listener was registered with container
        ArgumentCaptor<UnlockMessageListener> listenerCaptor = ArgumentCaptor.forClass(UnlockMessageListener.class);
        ArgumentCaptor<Topic> topicCaptor = ArgumentCaptor.forClass(Topic.class);
        
        verify(listenerContainer).addMessageListener(listenerCaptor.capture(), topicCaptor.capture());
        
        assertThat(listenerCaptor.getValue()).isSameAs(listener);
        assertThat(topicCaptor.getValue().getTopic()).isEqualTo("test:prefix:test-bucket:__unlock_channels__:*");
    }

    @Test
    @DisplayName("Should return existing listener for same bucket")
    void shouldReturnExistingListenerForSameBucket() {
        // Execute - create listener first time
        UnlockMessageListener firstCall = manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);
        
        // Execute - get listener second time
        UnlockMessageListener secondCall = manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);

        // Verify
        assertThat(firstCall).isSameAs(secondCall);
        
        // Verify listener was only registered once
        verify(listenerContainer, times(1)).addMessageListener(any(UnlockMessageListener.class), any(Topic.class));
    }

    @Test
    @DisplayName("Should create different listeners for different buckets")
    void shouldCreateDifferentListenersForDifferentBuckets() {
        String bucket1 = "bucket-1";
        String bucket2 = "bucket-2";
        
        // Execute
        UnlockMessageListener listener1 = manager.getOrCreateListenerForBucket(bucket1, KEY_PREFIX);
        UnlockMessageListener listener2 = manager.getOrCreateListenerForBucket(bucket2, KEY_PREFIX);

        // Verify
        assertThat(listener1).isNotSameAs(listener2);
        assertThat(listener1.getBucketName()).isEqualTo(bucket1);
        assertThat(listener2.getBucketName()).isEqualTo(bucket2);
        
        // Verify both listeners were registered
        verify(listenerContainer, times(2)).addMessageListener(any(UnlockMessageListener.class), any(Topic.class));
    }

    @Test
    @DisplayName("Should build channel pattern correctly")
    void shouldBuildChannelPatternCorrectly() {
        // Test various key prefixes
        String[] keyPrefixes = {
            "simple",
            "complex:prefix",
            "prefix:with:multiple:segments"
        };
        
        String[] expectedPatterns = {
            "simple:bucket-0:__unlock_channels__:*",
            "complex:prefix:bucket-1:__unlock_channels__:*",
            "prefix:with:multiple:segments:bucket-2:__unlock_channels__:*"
        };
        
        for (int i = 0; i < keyPrefixes.length; i++) {
            String bucketName = "bucket-" + i;
            UnlockMessageListener listener = manager.getOrCreateListenerForBucket(bucketName, keyPrefixes[i]);
            
            assertThat(listener.getChannelPattern()).isEqualTo(expectedPatterns[i]);
        }
    }

    @Test
    @DisplayName("Should create and manage semaphore holders")
    void shouldCreateAndManageSemaphoreHolders() {
        // Execute - create semaphore holder
        LockSemaphoreHolder holder = manager.getOrCreateSemaphoreHolder(BUCKET_NAME, KEY_PREFIX, LOCK_NAME);

        // Verify
        assertThat(holder).isNotNull();
        
        // Verify listener was created and registered
        verify(listenerContainer).addMessageListener(any(UnlockMessageListener.class), any(Topic.class));
        
        // Execute - remove semaphore holder
        LockSemaphoreHolder removed = manager.removeSemaphoreHolder(BUCKET_NAME, KEY_PREFIX, LOCK_NAME);
        
        // Verify
        assertThat(removed).isSameAs(holder);
    }

    @Test
    @DisplayName("Should return null when removing non-existent semaphore holder")
    void shouldReturnNullWhenRemovingNonExistentSemaphoreHolder() {
        // Execute - try to remove from non-existent bucket
        LockSemaphoreHolder removed = manager.removeSemaphoreHolder("non-existent-bucket", KEY_PREFIX, LOCK_NAME);

        // Verify
        assertThat(removed).isNull();
    }

    @Test
    @DisplayName("Should return null when removing from existing bucket but non-existent lock")
    void shouldReturnNullWhenRemovingFromExistingBucketButNonExistentLock() {
        // Setup - create listener for bucket
        manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);
        
        // Execute - try to remove non-existent lock
        LockSemaphoreHolder removed = manager.removeSemaphoreHolder(BUCKET_NAME, KEY_PREFIX, "non-existent-lock");

        // Verify
        assertThat(removed).isNull();
    }

    @Test
    @DisplayName("Should handle concurrent access to same bucket")
    void shouldHandleConcurrentAccessToSameBucket() throws InterruptedException {
        final int threadCount = 10;
        final UnlockMessageListener[] listeners = new UnlockMessageListener[threadCount];
        final Thread[] threads = new Thread[threadCount];
        
        // Create multiple threads trying to get listener for same bucket
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                listeners[index] = manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);
            });
        }
        
        // Start all threads
        for (Thread thread : threads) {
            thread.start();
        }
        
        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Verify all threads got the same listener instance
        UnlockMessageListener firstListener = listeners[0];
        assertThat(firstListener).isNotNull();
        
        for (int i = 1; i < threadCount; i++) {
            assertThat(listeners[i]).isSameAs(firstListener);
        }
        
        // Verify listener was only registered once
        verify(listenerContainer, times(1)).addMessageListener(any(UnlockMessageListener.class), any(Topic.class));
    }

    @Test
    @DisplayName("Should unregister all listeners during shutdown")
    void shouldUnregisterAllListenersDuringShutdown() {
        // Setup - create multiple listeners
        String bucket1 = "bucket-1";
        String bucket2 = "bucket-2";
        String bucket3 = "bucket-3";
        
        UnlockMessageListener listener1 = manager.getOrCreateListenerForBucket(bucket1, KEY_PREFIX);
        UnlockMessageListener listener2 = manager.getOrCreateListenerForBucket(bucket2, KEY_PREFIX);
        UnlockMessageListener listener3 = manager.getOrCreateListenerForBucket(bucket3, KEY_PREFIX);
        
        // Verify listeners were registered
        verify(listenerContainer, times(3)).addMessageListener(any(UnlockMessageListener.class), any(Topic.class));
        
        // Execute shutdown
        manager.shutdown();
        
        // Verify all listeners were unregistered
        verify(listenerContainer).removeMessageListener(listener1);
        verify(listenerContainer).removeMessageListener(listener2);
        verify(listenerContainer).removeMessageListener(listener3);
    }

    @Test
    @DisplayName("Should handle exceptions during shutdown gracefully")
    void shouldHandleExceptionsDuringShutdownGracefully() {
        // Setup - create listener
        UnlockMessageListener listener = manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);
        
        // Setup - make removeMessageListener throw exception
        doThrow(new RuntimeException("Test exception")).when(listenerContainer).removeMessageListener(listener);
        
        // Execute shutdown - should not throw exception
        assertThatCode(() -> manager.shutdown()).doesNotThrowAnyException();
        
        // Verify removeMessageListener was called despite exception
        verify(listenerContainer).removeMessageListener(listener);
    }

    @Test
    @DisplayName("Should handle empty bucket name")
    void shouldHandleEmptyBucketName() {
        // Execute
        UnlockMessageListener listener = manager.getOrCreateListenerForBucket("", KEY_PREFIX);

        // Verify
        assertThat(listener).isNotNull();
        assertThat(listener.getBucketName()).isEmpty();
        assertThat(listener.getChannelPattern()).isEqualTo("test:prefix::__unlock_channels__:*");
    }

    @Test
    @DisplayName("Should handle empty key prefix")
    void shouldHandleEmptyKeyPrefix() {
        // Execute
        UnlockMessageListener listener = manager.getOrCreateListenerForBucket(BUCKET_NAME, "");

        // Verify
        assertThat(listener).isNotNull();
        assertThat(listener.getChannelPattern()).isEqualTo(":test-bucket:__unlock_channels__:*");
    }
}
