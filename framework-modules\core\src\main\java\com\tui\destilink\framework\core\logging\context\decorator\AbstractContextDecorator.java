package com.tui.destilink.framework.core.logging.context.decorator;

import lombok.Getter;
import org.slf4j.MDC;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.net.URI;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class AbstractContextDecorator<S extends AbstractContextDecorator<S>> {

    private final String keyPrefix;

    protected AbstractContextDecorator(String keyPrefix) {
        Assert.notNull(keyPrefix, "KeyPrefix must not be null");
        this.keyPrefix = keyPrefix;
    }

    public void clear() {
        getKeys().forEach(this::remove);
    }

    public MultiScope createClosableScope() {
        return createClosableScope(this);
    }

    /**
     * Creates a closable scope that properly handles nested contexts by capturing
     * the current MDC state before applying this decorator's values.
     * This method should be used instead of the fluent API when nested scopes are needed.
     *
     * @return AutoCloseable that restores the previous MDC state when closed
     */
    public AutoCloseable createNestedScope() {
        // Capture current MDC state before applying any changes
        Map<String, String> previousContext = MDC.getCopyOfContextMap();

        // Apply this decorator's current property values to MDC
        copyCurrentProperties().forEach(MDC::put);

        // Return scope that restores previous context
        return () -> {
            if (previousContext != null) {
                MDC.setContextMap(previousContext);
            } else {
                MDC.clear();
            }
        };
    }

    protected void put(String key, String value) {
        MDC.put(key, value);
    }

    protected String get(String key) {
        return MDC.get(key);
    }

    protected void remove(String key) {
        MDC.remove(key);
    }

    protected Map<String, String> copyCurrentProperties() {
        Map<String, String> copy = new HashMap<>();
        getKeys().forEach(key -> {
            String value = MDC.get(key);
            if (value != null) {
                copy.put(key, value);
            }
        });
        return copy;
    }

    protected abstract Set<String> getKeys();

    protected Property<String> buildStringProperty(String keySuffix) {
        return buildProperty(keySuffix, Function.identity(), Function.identity());
    }

    protected Property<Boolean> buildBooleanProperty(String keySuffix) {
        return buildProperty(keySuffix, Boolean::valueOf, Object::toString);
    }

    protected Property<Integer> buildIntegerProperty(String keySuffix) {
        return buildProperty(keySuffix, Integer::valueOf, Object::toString);
    }

    protected Property<Long> buildLongProperty(String keySuffix) {
        return buildProperty(keySuffix, Long::valueOf, Object::toString);
    }

    protected Property<URI> buildUriProperty(String keySuffix) {
        return buildProperty(keySuffix, URI::create, URI::toString);
    }

    protected Property<OffsetDateTime> buildOffsetDateTimeProperty(String keySuffix) {
        return buildProperty(keySuffix, OffsetDateTime::parse, OffsetDateTime::toString);
    }

    protected Property<ZonedDateTime> buildZonedDateTimeProperty(String keySuffix) {
        return buildProperty(keySuffix, ZonedDateTime::parse, ZonedDateTime::toString);
    }

    protected Property<UUID> buildUUIDProperty(String keySuffix) {
        return buildProperty(keySuffix, UUID::fromString, UUID::toString);
    }

    @SuppressWarnings("unchecked")
    protected <T> Property<T> buildProperty(String keySuffix, Function<String, T> fromString, Function<T, String> toString) {
        return new Property<>((S) this, keyPrefix + keySuffix, fromString, toString);
    }

    public static MultiScope createClosableScope(AbstractContextDecorator<?>... contextDecorators) {
        Set<String> keys = Arrays.stream(contextDecorators).flatMap(cd -> cd.getKeys().stream()).collect(Collectors.toSet());

        // For single decorator (called from instance method), try to determine previous values
        if (contextDecorators.length == 1) {
            return createClosableScopeForSingleDecorator(contextDecorators[0], keys);
        }

        // For multiple decorators, use standard approach
        return createClosableScope(keys);
    }

    private static MultiScope createClosableScopeForSingleDecorator(AbstractContextDecorator<?> decorator, Set<String> keys) {
        // Get the decorator's current property values
        Map<String, String> decoratorValues = decorator.copyCurrentProperties();

        // Temporarily remove the decorator's values from MDC to see what was there before
        Map<String, String> previousValues = new HashMap<>();
        Map<String, String> removedValues = new HashMap<>();

        keys.forEach(key -> {
            String currentValue = MDC.get(key);
            String decoratorValue = decoratorValues.get(key);

            // If current MDC value matches decorator value, temporarily remove it
            if (Objects.equals(currentValue, decoratorValue)) {
                removedValues.put(key, currentValue);
                MDC.remove(key);

                // Check if there's a value underneath
                String underlyingValue = MDC.get(key);
                if (underlyingValue != null) {
                    previousValues.put(key, underlyingValue);
                }
            } else {
                // Current value doesn't match decorator value, keep it as previous
                if (currentValue != null) {
                    previousValues.put(key, currentValue);
                }
            }
        });

        // Restore the removed values
        removedValues.forEach(MDC::put);

        return new MultiScope(keys, previousValues);
    }

    public static MultiScope createClosableScope(String... keys) {
        return createClosableScope(Set.of(keys));
    }

    public static MultiScope createClosableScope(Set<String> keys) {
        Map<String, String> previousValues = HashMap.newHashMap(keys.size());
        keys.forEach(key -> {
            String value = MDC.get(key);
            if (value != null) {
                previousValues.put(key, value);
            }
        });
        return new MultiScope(keys, previousValues);
    }

    public class Property<T> {
        private final S delegate;
        @Getter
        private final String key;

        private final Function<String, T> fromString;
        private final Function<T, String> toString;

        public Property(S delegate, String key, Function<String, T> fromString, Function<T, String> toString) {
            this.delegate = delegate;
            this.key = key;
            this.fromString = fromString;
            this.toString = toString;
        }

        public S putAsObject(@Nullable Object value) {
            Optional.ofNullable(value)
                    .map(Object::toString)
                    .ifPresent(this::putAsString);
            return delegate;
        }

        public S putAsString(@Nullable String value) {
            Optional.ofNullable(value)
                    .map(fromString)
                    .ifPresent(this::put);
            return delegate;
        }

        public S put(@Nullable T value) {
            Optional.ofNullable(value)
                    .map(toString)
                    .ifPresent(v -> delegate.put(key, v));
            return delegate;
        }

        public S putOrRemoveAsObject(@Nullable Object value) {
            Optional.ofNullable(value)
                    .map(Object::toString)
                    .ifPresentOrElse(this::putOrRemoveAsString, () -> putOrRemove(null));
            return delegate;
        }

        public S putOrRemoveAsString(@Nullable String value) {
            Optional.ofNullable(value)
                    .map(fromString)
                    .ifPresentOrElse(this::putOrRemove, () -> putOrRemove(null));
            return delegate;
        }

        public S putOrRemove(@Nullable T value) {
            Optional.ofNullable(value)
                    .map(toString)
                    .ifPresentOrElse(v -> delegate.put(key, v), () -> delegate.remove(key));
            return delegate;
        }

        public Scope putCloseable(@Nullable T value) {
            final String previous = delegate.get(key);
            put(value);
            return () -> {
                if (previous != null) {
                    delegate.put(key, previous);
                } else {
                    delegate.remove(key);
                }
            };
        }

        public T get() {
            return Optional.ofNullable(delegate.get(key)).map(fromString).orElse(null);
        }

        public S remove() {
            delegate.remove(key);
            return delegate;
        }
    }
}
