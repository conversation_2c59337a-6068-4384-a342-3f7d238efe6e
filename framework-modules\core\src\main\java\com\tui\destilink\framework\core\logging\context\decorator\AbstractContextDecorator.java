package com.tui.destilink.framework.core.logging.context.decorator;

import lombok.Getter;
import org.slf4j.MDC;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.net.URI;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class AbstractContextDecorator<S extends AbstractContextDecorator<S>> {

    private final String keyPrefix;

    protected AbstractContextDecorator(String keyPrefix) {
        Assert.notNull(keyPrefix, "KeyPrefix must not be null");
        this.keyPrefix = keyPrefix;
    }

    public void clear() {
        getKeys().forEach(this::remove);
    }

    public MultiScope createClosableScope() {
        return createClosableScope(this);
    }

    protected void put(String key, String value) {
        MDC.put(key, value);
    }

    protected String get(String key) {
        return MDC.get(key);
    }

    protected void remove(String key) {
        MDC.remove(key);
    }

    protected Map<String, String> copyCurrentProperties() {
        Map<String, String> copy = new HashMap<>();
        getKeys().forEach(key -> {
            String value = MDC.get(key);
            if (value != null) {
                copy.put(key, value);
            }
        });
        return copy;
    }

    protected abstract Set<String> getKeys();

    protected Property<String> buildStringProperty(String keySuffix) {
        return buildProperty(keySuffix, Function.identity(), Function.identity());
    }

    protected Property<Boolean> buildBooleanProperty(String keySuffix) {
        return buildProperty(keySuffix, Boolean::valueOf, Object::toString);
    }

    protected Property<Integer> buildIntegerProperty(String keySuffix) {
        return buildProperty(keySuffix, Integer::valueOf, Object::toString);
    }

    protected Property<Long> buildLongProperty(String keySuffix) {
        return buildProperty(keySuffix, Long::valueOf, Object::toString);
    }

    protected Property<URI> buildUriProperty(String keySuffix) {
        return buildProperty(keySuffix, URI::create, URI::toString);
    }

    protected Property<OffsetDateTime> buildOffsetDateTimeProperty(String keySuffix) {
        return buildProperty(keySuffix, OffsetDateTime::parse, OffsetDateTime::toString);
    }

    protected Property<ZonedDateTime> buildZonedDateTimeProperty(String keySuffix) {
        return buildProperty(keySuffix, ZonedDateTime::parse, ZonedDateTime::toString);
    }

    protected Property<UUID> buildUUIDProperty(String keySuffix) {
        return buildProperty(keySuffix, UUID::fromString, UUID::toString);
    }

    @SuppressWarnings("unchecked")
    protected <T> Property<T> buildProperty(String keySuffix, Function<String, T> fromString, Function<T, String> toString) {
        return new Property<>((S) this, keyPrefix + keySuffix, fromString, toString);
    }

    public static MultiScope createClosableScope(AbstractContextDecorator<?>... contextDecorators) {
        Set<String> keys = Arrays.stream(contextDecorators).flatMap(cd -> cd.getKeys().stream()).collect(Collectors.toSet());
        return createClosableScope(keys);
    }

    public static MultiScope createClosableScope(String... keys) {
        return createClosableScope(Set.of(keys));
    }

    public static MultiScope createClosableScope(Set<String> keys) {
        Map<String, String> previousValues = HashMap.newHashMap(keys.size());
        keys.forEach(key -> {
            String value = MDC.get(key);
            if (value != null) {
                previousValues.put(key, value);
            }
        });
        return new MultiScope(keys, previousValues);
    }

    public class Property<T> {
        private final S delegate;
        @Getter
        private final String key;

        private final Function<String, T> fromString;
        private final Function<T, String> toString;

        public Property(S delegate, String key, Function<String, T> fromString, Function<T, String> toString) {
            this.delegate = delegate;
            this.key = key;
            this.fromString = fromString;
            this.toString = toString;
        }

        public S putAsObject(@Nullable Object value) {
            Optional.ofNullable(value)
                    .map(Object::toString)
                    .ifPresent(this::putAsString);
            return delegate;
        }

        public S putAsString(@Nullable String value) {
            Optional.ofNullable(value)
                    .map(fromString)
                    .ifPresent(this::put);
            return delegate;
        }

        public S put(@Nullable T value) {
            Optional.ofNullable(value)
                    .map(toString)
                    .ifPresent(v -> delegate.put(key, v));
            return delegate;
        }

        public S putOrRemoveAsObject(@Nullable Object value) {
            Optional.ofNullable(value)
                    .map(Object::toString)
                    .ifPresentOrElse(this::putOrRemoveAsString, () -> putOrRemove(null));
            return delegate;
        }

        public S putOrRemoveAsString(@Nullable String value) {
            Optional.ofNullable(value)
                    .map(fromString)
                    .ifPresentOrElse(this::putOrRemove, () -> putOrRemove(null));
            return delegate;
        }

        public S putOrRemove(@Nullable T value) {
            Optional.ofNullable(value)
                    .map(toString)
                    .ifPresentOrElse(v -> delegate.put(key, v), () -> delegate.remove(key));
            return delegate;
        }

        public Scope putCloseable(@Nullable T value) {
            final String previous = delegate.get(key);
            put(value);
            return () -> {
                if (previous != null) {
                    delegate.put(key, previous);
                } else {
                    delegate.remove(key);
                }
            };
        }

        public T get() {
            return Optional.ofNullable(delegate.get(key)).map(fromString).orElse(null);
        }

        public S remove() {
            delegate.remove(key);
            return delegate;
        }
    }
}
