package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Test configuration that provides mocked Redis components for testing.
 * This configuration replaces the real Redis dependencies with mocks that simulate
 * the expected behavior for testing purposes.
 */
@TestConfiguration
public class TestRedisLockConfiguration {

    // Shared state for simulating Redis lock storage
    private static final Map<String, LockEntry> lockEntries = new ConcurrentHashMap<>();

    /**
     * Represents a lock entry in our mock Redis storage
     */
    private static class LockEntry {
        private final String owner;
        private long expirationTime;

        public LockEntry(String owner, long ttlMs) {
            this.owner = owner;
            this.expirationTime = System.currentTimeMillis() + ttlMs;
        }

        public String getOwner() {
            return owner;
        }

        public long getExpirationTime() {
            return expirationTime;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expirationTime;
        }

        public void updateExpiration(long ttlMs) {
            this.expirationTime = System.currentTimeMillis() + ttlMs;
        }
    }

    @Bean
    @Primary
    public RedisLockOperations mockRedisLockOperations() {
        RedisLockOperations mock = mock(RedisLockOperations.class);

        // Mock tryLock method
        when(mock.tryLock(any(), any(), any()))
                .thenAnswer(invocation -> {
                    String lockKey = invocation.getArgument(0);
                    String ownerId = invocation.getArgument(1);
                    Duration ttl = invocation.getArgument(2);

                    // Validate parameters and return failed future if invalid
                    try {
                        validateLockParameters(lockKey, ownerId, ttl);
                    } catch (IllegalArgumentException e) {
                        return CompletableFuture.failedFuture(e);
                    }

                    long ttlMs = ttl.toMillis();

                    // Clean up expired locks
                    lockEntries.entrySet().removeIf(entry -> entry.getValue().isExpired());

                    LockEntry entry = lockEntries.get(lockKey);

                    if (entry == null || entry.isExpired()) {
                        // Lock doesn't exist or is expired - acquire it
                        lockEntries.put(lockKey, new LockEntry(ownerId, ttlMs));
                        return CompletableFuture.completedFuture(true);
                    } else if (entry.getOwner().equals(ownerId)) {
                        // Lock is already held by the same owner - idempotent success (reentrant)
                        entry.updateExpiration(ttlMs);
                        return CompletableFuture.completedFuture(true);
                    } else {
                        // Lock is held by a different owner - cannot acquire
                        return CompletableFuture.completedFuture(false);
                    }
                });

        // Mock unlock method (returns CompletableFuture<Void>)
        when(mock.unlock(any(), any()))
                .thenAnswer(invocation -> {
                    String lockKey = invocation.getArgument(0);
                    String ownerId = invocation.getArgument(1);

                    // Validate parameters and return failed future if invalid
                    try {
                        validateBasicLockParameters(lockKey, ownerId);
                    } catch (IllegalArgumentException e) {
                        return CompletableFuture.failedFuture(e);
                    }

                    // Clean up expired locks
                    lockEntries.entrySet().removeIf(entry -> entry.getValue().isExpired());

                    LockEntry entry = lockEntries.get(lockKey);

                    if (entry == null || entry.isExpired()) {
                        // Lock doesn't exist or is expired - already unlocked (this is still success for unlock)
                        return CompletableFuture.completedFuture(null);
                    } else if (entry.getOwner().equals(ownerId)) {
                        // Lock is held by the requesting owner - unlock it
                        lockEntries.remove(lockKey);
                        return CompletableFuture.completedFuture(null);
                    } else {
                        // Lock is held by a different owner - cannot unlock (this should fail)
                        return CompletableFuture.failedFuture(new RuntimeException("Lock held by different owner"));
                    }
                });

        // Mock releaseLock method (returns CompletableFuture<Boolean>)
        when(mock.releaseLock(any(), any()))
                .thenAnswer(invocation -> {
                    String lockKey = invocation.getArgument(0);
                    String ownerId = invocation.getArgument(1);

                    // Validate parameters and return failed future if invalid
                    try {
                        validateBasicLockParameters(lockKey, ownerId);
                    } catch (IllegalArgumentException e) {
                        return CompletableFuture.failedFuture(e);
                    }

                    // Clean up expired locks
                    lockEntries.entrySet().removeIf(entry -> entry.getValue().isExpired());

                    LockEntry entry = lockEntries.get(lockKey);

                    if (entry == null || entry.isExpired()) {
                        // Lock doesn't exist or is expired - return false (already unlocked)
                        return CompletableFuture.completedFuture(false);
                    } else if (entry.getOwner().equals(ownerId)) {
                        // Lock is held by the requesting owner - unlock it and return true
                        lockEntries.remove(lockKey);
                        return CompletableFuture.completedFuture(true);
                    } else {
                        // Lock is held by a different owner - cannot unlock, return false
                        return CompletableFuture.completedFuture(false);
                    }
                });

        // Mock extendLock method
        when(mock.extendLock(any(), any(), any()))
                .thenAnswer(invocation -> {
                    String lockKey = invocation.getArgument(0);
                    String ownerId = invocation.getArgument(1);
                    Duration ttl = invocation.getArgument(2);

                    // Validate parameters and return failed future if invalid
                    try {
                        validateLockParameters(lockKey, ownerId, ttl);
                    } catch (IllegalArgumentException e) {
                        return CompletableFuture.failedFuture(e);
                    }

                    long ttlMs = ttl.toMillis();

                    // Clean up expired locks
                    lockEntries.entrySet().removeIf(entry -> entry.getValue().isExpired());

                    LockEntry entry = lockEntries.get(lockKey);

                    if (entry == null || entry.isExpired()) {
                        // Lock doesn't exist or is expired - cannot extend
                        return CompletableFuture.completedFuture(false);
                    } else if (entry.getOwner().equals(ownerId)) {
                        // Lock is held by the requesting owner - extend it (idempotent)
                        entry.updateExpiration(ttlMs);
                        return CompletableFuture.completedFuture(true);
                    } else {
                        // Lock is held by a different owner - cannot extend
                        return CompletableFuture.completedFuture(false);
                    }
                });

        // Mock isLocked method
        when(mock.isLocked(any()))
                .thenAnswer(invocation -> {
                    String lockKey = invocation.getArgument(0);

                    // Validate parameters and return failed future if invalid
                    if (lockKey == null) {
                        return CompletableFuture.failedFuture(new IllegalArgumentException("lockKey cannot be null"));
                    }
                    if (lockKey.trim().isEmpty()) {
                        return CompletableFuture.failedFuture(new IllegalArgumentException("lockKey cannot be empty"));
                    }

                    // Clean up expired locks
                    lockEntries.entrySet().removeIf(entry -> entry.getValue().isExpired());

                    LockEntry entry = lockEntries.get(lockKey);
                    boolean isLocked = entry != null && !entry.isExpired();
                    return CompletableFuture.completedFuture(isLocked);
                });

        // Mock checkLock method
        when(mock.checkLock(any(), any()))
                .thenAnswer(invocation -> {
                    String lockKey = invocation.getArgument(0);
                    String ownerId = invocation.getArgument(1);

                    // Validate parameters and return failed future if invalid
                    try {
                        validateBasicLockParameters(lockKey, ownerId);
                    } catch (IllegalArgumentException e) {
                        return CompletableFuture.failedFuture(e);
                    }

                    // Clean up expired locks
                    lockEntries.entrySet().removeIf(entry -> entry.getValue().isExpired());

                    LockEntry entry = lockEntries.get(lockKey);
                    boolean isHeldByOwner = entry != null && !entry.isExpired() && entry.getOwner().equals(ownerId);
                    return CompletableFuture.completedFuture(isHeldByOwner);
                });

        // Mock acquireLock method (alias for tryLock)
        when(mock.acquireLock(any(), any(), any()))
                .thenAnswer(invocation -> {
                    String lockKey = invocation.getArgument(0);
                    String ownerId = invocation.getArgument(1);
                    Duration ttl = invocation.getArgument(2);

                    // Delegate to tryLock implementation
                    return mock.tryLock(lockKey, ownerId, ttl);
                });

        // Mock tryLock with timeout method
        when(mock.tryLock(any(), any(), any(), any()))
                .thenAnswer(invocation -> {
                    String lockKey = invocation.getArgument(0);
                    String ownerId = invocation.getArgument(1);
                    Duration ttl = invocation.getArgument(2);
                    Duration timeout = invocation.getArgument(3);

                    // For simplicity, just delegate to regular tryLock
                    // In a real implementation, this would handle timeout logic
                    return mock.tryLock(lockKey, ownerId, ttl);
                });

        return mock;
    }

    /**
     * Validates lock operation parameters and throws IllegalArgumentException if invalid.
     */
    private void validateLockParameters(String lockKey, String ownerId, Duration ttl) {
        if (lockKey == null) {
            throw new IllegalArgumentException("lockKey cannot be null");
        }
        if (lockKey.trim().isEmpty()) {
            throw new IllegalArgumentException("lockKey cannot be empty");
        }
        if (ownerId == null) {
            throw new IllegalArgumentException("ownerId cannot be null");
        }
        if (ownerId.trim().isEmpty()) {
            throw new IllegalArgumentException("ownerId cannot be empty");
        }
        if (ttl == null) {
            throw new IllegalArgumentException("leaseTime cannot be null");
        }
        if (ttl.isNegative()) {
            throw new IllegalArgumentException("leaseTime cannot be negative");
        }
        if (ttl.isZero()) {
            throw new IllegalArgumentException("leaseTime cannot be zero");
        }
    }

    /**
     * Validates basic lock operation parameters (lockKey and ownerId only).
     */
    private void validateBasicLockParameters(String lockKey, String ownerId) {
        if (lockKey == null) {
            throw new IllegalArgumentException("lockKey cannot be null");
        }
        if (lockKey.trim().isEmpty()) {
            throw new IllegalArgumentException("lockKey cannot be empty");
        }
        if (ownerId == null) {
            throw new IllegalArgumentException("ownerId cannot be null");
        }
        if (ownerId.trim().isEmpty()) {
            throw new IllegalArgumentException("ownerId cannot be empty");
        }
    }

}
