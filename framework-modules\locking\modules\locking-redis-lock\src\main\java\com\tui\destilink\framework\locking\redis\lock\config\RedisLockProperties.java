package com.tui.destilink.framework.locking.redis.lock.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;

/**
 * Configuration properties for the Redis-based distributed locking module.
 * <p>
 * This class defines the global configuration for Redis locks, including:
 * <ul>
 * <li>Default lock behavior (lease times, retry logic, timeouts)</li>
 * <li>Watchdog configuration for automatic lock extension</li>
 * <li>Retry and circuit breaker settings for resilience</li>
 * <li>Idempotency cache settings for operation safety</li>
 * </ul>
 * <p>
 * The configuration follows a clear precedence hierarchy:
 * Instance-specific settings (via builders) > Bucket Configuration > Global Configuration (this class).
 * <p>
 * Key features implemented:
 * <ul>
 * <li>Virtual Thread integration for improved I/O handling</li>
 * <li>Lua-only Redis operations for atomicity and consistency</li>
 * <li>Mandatory lock-type segments for semantic isolation</li>
 * <li>Always-active watchdog with conditional monitoring</li>
 * <li>Centralized idempotency management</li>
 * </ul>
 * <p>
 * <strong>Note:</strong> This configuration has been cleaned up as part of Step 9.8.
 * Removed properties: lockOwnerIdValidationRegex (hardcoded in LockOwnerSupplier),
 * length validation properties, and unnecessary executor configurations.
 * </p>
 *
 * @see com.tui.destilink.framework.locking.redis.lock.docs.configuration
 */
@Getter
@ToString
@RequiredArgsConstructor // For final fields, Spring Boot will use this for property binding
@Validated
@ConfigurationProperties(prefix = "destilink.fw.locking.redis")
public class RedisLockProperties {

    /** Whether the Redis locking mechanism is enabled. */
    private final boolean enabled;

    /** The expiration time for the state key. */
    @NotNull
    private final Duration stateKeyExpiration;

    /**
     * Default Redis lock properties, applied to all locks unless overridden at the
     * bucket or individual lock level.
     */
    @NotNull
    @Valid
    @NestedConfigurationProperty // Ensures this nested object is treated as a configuration property group
    private final Defaults defaults;

    /**
     * Configuration for the lock watchdog mechanism.
     */
    @NotNull
    @Valid
    @NestedConfigurationProperty // Ensures this nested object is treated as a configuration property group
    private final WatchdogProperties watchdog;

    /**
     * Defines the Time-To-Live (TTL) for idempotency records in Redis. These
     * records are stored in a response cache, keyed by a unique request UUID
     * generated per logical operation within {@code RedisLockOperationsImpl}.
     * <p>
     * This mechanism ensures that if a client retries an operation (e.g., lock,
     * unlock, extend) due to a lost response, the operation is not executed
     * multiple times if the original attempt succeeded. All mutating Lua scripts
     * implement mandatory idempotency checks using this cache.
     * <p>
     * The TTL should be long enough to cover typical client retry windows and
     * network timeout scenarios. The idempotency mechanism, including the
     * generation of the request UUID and management of the response cache, is
     * centralized within {@code RedisLockOperationsImpl} and enforced by all
     * underlying Lua scripts.
     * <p>
     * <strong>Critical:</strong> Every mutating Redis operation (lock acquisition,
     * unlock, lease extension, state updates) MUST use this idempotency mechanism
     * to prevent race conditions and ensure consistency in distributed environments.
     */
    @NotNull
    private final Duration responseCacheTtl;



    /**
     * Gets the Time-To-Live (TTL) for idempotency records in Redis.
     * These records ensure that retried operations (e.g., due to lost responses)
     * are not executed multiple times
     * if the original attempt succeeded. The TTL should cover typical client retry
     * windows.
     *
     * @return the response cache TTL.
     */
    public Duration getResponseCacheTtl() {
        return responseCacheTtl;
    }

    /**
     * Default properties for lock implementations.
     * <p>
     * These values serve as the baseline configuration for all locks and can be overridden
     * at the bucket level or individual lock level via builders. All timing values are
     * optimized for Virtual Thread usage and distributed environments.
     * <p>
     * Configured under 'destilink.fw.locking.redis.defaults'.
     *
     * @see com.tui.destilink.framework.locking.redis.lock.docs.configuration
     */
    @Getter
    @ToString
    @Validated // Added for validation of the properties within this nested class
    public static class Defaults {

        /**
         * The default lease time for a lock. This is the initial TTL for the lock in
         * Redis.
         */
        @NotNull
        private Duration leaseTime = Duration.ofSeconds(60);

        /**
         * The default interval between retry attempts when trying to acquire a lock.
         */
        @NotNull
        private Duration retryInterval = Duration.ofMillis(100);

        /**
         * The default maximum number of retry attempts when trying to acquire a lock. 0
         * means no retries beyond the initial attempt.
         */
        @Min(0)
        private int maxRetries = 3;

        /**
         * The default timeout for the entire lock acquisition operation (e.g., for
         * tryLock(timeout, unit)).
         */
        @NotNull
        private Duration acquireTimeout = Duration.ofSeconds(30);

        // Setters for Spring Boot property binding
        public void setLeaseTime(Duration leaseTime) {
            this.leaseTime = leaseTime;
        }

        public void setRetryInterval(Duration retryInterval) {
            this.retryInterval = retryInterval;
        }

        public void setMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
        }

        public void setAcquireTimeout(Duration acquireTimeout) {
            this.acquireTimeout = acquireTimeout;
        }

    }

    /**
     * Configuration properties for retry logic in Redis lock operations.
     * These properties are configured under 'destilink.fw.locking.redis.retry'.
     */
    @NotNull
    @Valid
    @NestedConfigurationProperty
    private final RetryConfig retry;

    /**
     * Configuration properties for the LockWatchdog.
     * These properties are configured under 'destilink.fw.locking.redis.watchdog'.
     *
     * The watchdog is always active and conditionally monitors locks based on their
     * safetyBuffer calculation. It uses PEXPIREAT for precise expiration management.
     */
    @Getter
    @ToString
    public static class WatchdogProperties {

        /**
         * Interval at which the watchdog checks for locks that need lease extension.
         * This should be significantly shorter than typical lock lease times.
         * Default: 10 seconds
         */
        @NotNull
        private Duration interval = Duration.ofSeconds(10);

        /**
         * Factor used to calculate the safety buffer for watchdog eligibility.
         * The safety buffer is calculated as: originalLeaseTimeMillis * factor
         *
         * Locks are eligible for watchdog monitoring if:
         * (expiresAtMillis - currentTimeMillis) <= safetyBuffer
         *
         * A factor of 0.3 means locks are monitored when they have 30% or less
         * of their original lease time remaining.
         *
         * Valid range: 0.1 to 0.9
         * Default: 0.3
         */
        @DecimalMin(value = "0.1", message = "Factor must be at least 0.1")
        @DecimalMax(value = "0.9", message = "Factor must be at most 0.9")
        private double factor = 0.3;

        /**
         * Core pool size for the watchdog's scheduled executor service.
         * Default: 2
         */
        @Min(1)
        private int corePoolSize = 2;

        /**
         * Thread name prefix for watchdog executor threads.
         * Default: "redis-lock-watchdog-"
         */
        @NotBlank
        private String threadNamePrefix = "redis-lock-watchdog-";

        /**
         * Timeout for waiting for the watchdog scheduler to terminate during shutdown.
         * Default: 30 seconds
         */
        @NotNull
        private Duration shutdownAwaitTermination = Duration.ofSeconds(30);

        // Setters for Spring Boot property binding
        public void setInterval(Duration interval) {
            this.interval = interval;
        }

        public void setFactor(double factor) {
            this.factor = factor;
        }

        public void setCorePoolSize(int corePoolSize) {
            this.corePoolSize = corePoolSize;
        }

        public void setThreadNamePrefix(String threadNamePrefix) {
            this.threadNamePrefix = threadNamePrefix;
        }

        public void setShutdownAwaitTermination(Duration shutdownAwaitTermination) {
            this.shutdownAwaitTermination = shutdownAwaitTermination;
        }
    }

    /**
     * Configuration properties for retry logic in Redis lock operations.
     * <p>
     * This configuration supports resilient operation in distributed environments
     * with Virtual Thread-friendly delays and circuit breaker patterns.
     * All retry operations use Thread.sleep() for Virtual Thread compatibility.
     * <p>
     * Configured under 'destilink.fw.locking.redis.retry'.
     */
    @Getter
    @ToString
    @Validated
    public static class RetryConfig {

        /**
         * Maximum number of retry attempts for Redis operations.
         * Default: 3 attempts (initial + 2 retries)
         */
        @Min(1)
        private int maxAttempts = 3;

        /**
         * Initial delay between retry attempts.
         * Default: 100ms
         */
        @NotNull
        private Duration initialDelay = Duration.ofMillis(100);

        /**
         * Backoff multiplier for exponential backoff.
         * Each retry delay = previous delay * backoffMultiplier
         * Default: 2.0 (doubles each time)
         */
        @Min(1)
        private double backoffMultiplier = 2.0;

        /**
         * Maximum delay between retry attempts.
         * Prevents exponential backoff from growing too large.
         * Default: 5 seconds
         */
        @NotNull
        private Duration maxDelay = Duration.ofSeconds(5);

        /**
         * Whether to add random jitter to retry delays.
         * Helps prevent thundering herd problems.
         * Default: true
         */
        private boolean jitterEnabled = true;

        /**
         * Circuit breaker failure threshold.
         * Number of consecutive failures before opening the circuit.
         * Default: 5
         */
        @Min(1)
        private int circuitBreakerFailureThreshold = 5;

        /**
         * Circuit breaker recovery timeout.
         * Time to wait before attempting to close the circuit.
         * Default: 30 seconds
         */
        @NotNull
        private Duration circuitBreakerRecoveryTimeout = Duration.ofSeconds(30);

        // Setters for Spring Boot property binding
        public void setMaxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
        }

        public void setInitialDelay(Duration initialDelay) {
            this.initialDelay = initialDelay;
        }

        public void setBackoffMultiplier(double backoffMultiplier) {
            this.backoffMultiplier = backoffMultiplier;
        }

        public void setMaxDelay(Duration maxDelay) {
            this.maxDelay = maxDelay;
        }

        public void setJitterEnabled(boolean jitterEnabled) {
            this.jitterEnabled = jitterEnabled;
        }

        public void setCircuitBreakerFailureThreshold(int circuitBreakerFailureThreshold) {
            this.circuitBreakerFailureThreshold = circuitBreakerFailureThreshold;
        }

        public void setCircuitBreakerRecoveryTimeout(Duration circuitBreakerRecoveryTimeout) {
            this.circuitBreakerRecoveryTimeout = circuitBreakerRecoveryTimeout;
        }
    }
}